package com.smartcar.easylauncher.modules.touch.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;

/**
 * 控制面板视图类，继承自FrameLayout，主要用于显示一个可展开和收起的控制面板。
 */
public class ControlPanelView extends FrameLayout {

    // 控件状态相关字段

    /**
     * 开关状态，表示面板是否打开
     */
    private boolean isOpen = false;

    /**
     * 面板的当前半径，开关动画时会用到
     */
    private int mPanelRadius;

    // 布局和尺寸相关字段

    /**
     * 半圆的原心位置到子控件之间的距离
     */
    private int mRadius;

    /**
     * 子控件的半径
     */
    private int mChildRadius;

    /**
     * 按钮的中心点X坐标
     */
    private int mButtonCenterX;

    /**
     * 按钮的中心点Y坐标
     */
    private int mButtonCenterY;

    // 动画相关字段

    /**
     * 打开面板的动画
     */
    private ValueAnimator mOpenAnimator;

    /**
     * 关闭面板的动画
     */
    private ValueAnimator mOffAnimator;

    // 自定义属性相关字段

    /**
     * 第一个按钮的角度
     */
    private int mStartAngle = -90;

    /**
     * 面板的宽度
     */
    private int mWidth;

    /**
     * 面板的高度
     */
    private int mHeight;

    /**
     * 面板是否在屏幕左边
     */
    private boolean isLeft = false;

    /**
     * 左边或右边的偏移量
     */
    private int mOffset;

    // 回调接口相关字段

    /**
     * 面板开关状态变化的监听器
     */
    private OnTogglePanelListener mTogglePanelListener;

    /**
     * 面板尺寸变化时的回调
     */
    private OnPanelSizeChangeCallback mOnPanelSizeChangeCallback;

    // 构造函数

    /**
     * 用于在代码中创建ControlPanelView对象的构造函数
     *
     * @param context 上下文环境
     */
    public ControlPanelView(Context context) {
        this(context, null);
    }

    /**
     * 用于在XML布局文件中使用该自定义控件的构造函数
     *
     * @param context 上下文环境
     * @param attrs   属性集合
     */
    public ControlPanelView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * 用于在XML布局文件中使用该自定义控件，并且还可以在布局文件中设置style样式的构造函数
     *
     * @param context         上下文环境
     * @param attrs           属性集合
     * @param defStyleAttr    默认的样式属性
     */
    public ControlPanelView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    // 初始化方法

    /**
     * 初始化一些变量和设置
     *
     * @param context 上下文环境
     * @param attrs   属性集合
     */
    private void init(Context context, AttributeSet attrs) {
        setWillNotDraw(false); // 允许绘制背景
        this.mOffset = 0;
        setStartAngle();
    }

    /**
     * 设置开始角度
     * 根据面板是否在屏幕左边来确定按钮的开始角度
     */
    private void setStartAngle() {
        if (isLeft) {
            mStartAngle = -90;
        } else {
            mStartAngle = 90;
        }
    }

    // 当控件大小发生改变时，这个方法会被调用
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        this.mWidth = w;
        this.mHeight = h;
        if (mOnPanelSizeChangeCallback != null) {
            mOnPanelSizeChangeCallback.onPanelSizeChange(mWidth, mHeight);
        }
    }

    // 测量控件大小
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);
        int count = getChildCount();
        // 重新计算所有子View的最大半径，避免只取最后一个的尺寸
        int maxChildRadius = 0;
        for (int i = 0; i < count; i++) {
            View childView = getChildAt(i);
            measureChild(childView, widthMeasureSpec, heightMeasureSpec);
            int r = Math.max(childView.getMeasuredWidth(), childView.getMeasuredHeight()) / 2;
            if (r > maxChildRadius) maxChildRadius = r;
        }
        mChildRadius = maxChildRadius;
        // 计算半圆的圆心到子控件圆心的距离（半径），预留边距 16dp
        int edgeMargin = DensityUtils.dp2px(getContext(), 16);
        mRadius = Math.max(widthSize, heightSize) / 2 - (mChildRadius * 2) - edgeMargin;
        if (mRadius < 1) { mRadius = 1; }
        // 计算按钮的位置，左、右的中间（使用当前测量宽高，避免未更新的成员变量）
        if (isLeft) {
            mButtonCenterX = mOffset;
            mButtonCenterY = heightSize / 2;
        } else {
            mButtonCenterX = widthSize - mOffset;
            mButtonCenterY = heightSize / 2;
        }
        setMeasuredDimension(widthSize, heightSize);
    }

    // 当布局加载完成后，这个方法会被调用
    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        if (isInEditMode()) {
            return;
        }
        int childCount = getChildCount();
        //一开始，是关闭状态，子View全部隐藏
        for (int i = 0; i < childCount; i++) {
            getChildAt(i).setVisibility(View.GONE);
        }
    }

    // 布局子控件
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        int count = getChildCount();
        if (count <= 0) {
            return;
        }
        //每份子控件应该占用的角度（count>=1安全）
        int childAngle = 180 / count;
        //每个子View之间的间隔（count==1时为0，避免除0）
        int interval = (count > 1) ? (180 / count) / (count - 1) : 0;
        for (int i = 0; i < count; i++) {
            //计算出每个子控件的位置
            float[] point;
            //因为在左边时，圆弧的开始角度是从-90度开始的，子View是顺时针从上到下排布
            //右边时，开始角度是90度，顺时针从下往上排布，但是我们的效果是从-90度逆时针从上往下排布
            //所以当右边时，将坐标位置和对应方向的位置调换，例如现在位置是0的子View，就要排布到4的位置，就是(count - 1) - i
            if (!isLeft) {
                int fixPosition = count - 1 - i;
                point = getCoordinatePoint(mPanelRadius, mStartAngle + ((fixPosition * childAngle) + (fixPosition * interval)));
            } else {
                point = getCoordinatePoint(mPanelRadius, mStartAngle + ((i * childAngle) + (i * interval)));
            }
            View childView = getChildAt(i);
            int childViewWidth = childView.getMeasuredWidth();
            int childViewHeight = childView.getMeasuredHeight();
            //int halfWidth = childViewWidth / 2;
            int halfHeight = childViewHeight / 2;
            //布局子控件
            int pointX = (int) point[0];
            int pointY = (int) point[1];
            if (isLeft) {
                childView.layout(pointX, pointY - halfHeight, pointX + childViewWidth, pointY + halfHeight);
            } else {
                childView.layout(pointX - childViewWidth, (pointY - halfHeight), (pointX), (pointY + halfHeight));
            }
        }
    }

    /**
     * 依圆心坐标，半径，扇形角度，计算出扇形终射线与圆弧交叉点的xy坐标
     *
     * @param angle 每个子控件和面板圆心的夹角
     */
    public float[] getCoordinatePoint(int panelRadius, float angle) {
        float[] point = new float[2];
        //Math类的三角函数是弧度制，所以要将角度转换为弧度才能进行计算
        double arcAngle = Math.toRadians(angle);
        //求子控件的X坐标，邻边 / 斜边，斜边的值刚好就是半径，cos值乘以斜边，就能求出邻边，而这个邻边的长度，就是点的x坐标
        point[0] = (float) (mButtonCenterX + Math.cos(arcAngle) * panelRadius);
        //求子控件的Y坐标，对边 / 斜边，斜边的值刚好就是半径，sin值乘以斜边，就能求出对边，而这个对边的长度，就是点的y坐标
        point[1] = (float) (mButtonCenterY + Math.sin(arcAngle) * panelRadius);
        return point;
    }

    public void offNow() {
        if (isOpen) {
            startOffAnimation();
            isOpen = !isOpen;
        }
    }

    public void openNow() {
        if (!isOpen) {
            startOpenAnimation();
            isOpen = !isOpen;
        }
    }

    public boolean isOpen() {
        return isOpen;
    }

    public boolean isAnimationRunning() {
        if (mOpenAnimator != null && mOpenAnimator.isRunning()) {
            return true;
        }
        return mOffAnimator != null && mOffAnimator.isRunning();
    }

    /**
     * 打开动画
     */
    private void startOpenAnimation() {
        // 如果打开动画已经在运行，就直接返回，不再执行后续代码
        if (mOpenAnimator != null && mOpenAnimator.isRunning()) {
            return;
        }
        // 创建一个值动画，值从0变化到mRadius
        mOpenAnimator = ValueAnimator.ofInt(0, mRadius);
        // 设置动画的持续时间为250毫秒
        mOpenAnimator.setDuration(250);
        // 设置动画的插值器，这里使用了一个减速插值器
        mOpenAnimator.setInterpolator(AnimationUtils.loadInterpolator(getContext(), R.anim.decelerate_interpolator_more));
        // 设置动画的更新监听器
        mOpenAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                // 获取当前动画的值
                int cValue = (int) valueAnimator.getAnimatedValue();
                // 计算当前的透明度（避免除0）
                float alpha = mRadius > 0 ? (cValue * 1f / mRadius) : 1f;
                // 获取子控件的数量
                int childCount = getChildCount();
                // 遍历所有的子控件，设置它们的透明度
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setAlpha(alpha);
                }
                // 更新面板半径
                mPanelRadius = cValue;
                // 请求重新布局
                requestLayout();
            }
        });
        // 设置动画的监听器
        mOpenAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                // 当动画开始时，将所有的子控件设置为可见
                int childCount = getChildCount();
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setVisibility(View.VISIBLE);
                }
                // 如果设置了面板切换监听器，那么通知监听器面板已经打开
                if (mTogglePanelListener != null) {
                    mTogglePanelListener.onToggleChange(true);
                }
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                // 当动画结束时，将mOpenAnimator设置为null
                mOpenAnimator = null;
            }
        });
        // 开始动画
        mOpenAnimator.start();
    }

    /**
     * 关闭动画
     */
    private void startOffAnimation() {
        // 如果关闭动画已经在运行，就直接返回，不再执行后续代码
        if (mOffAnimator != null && mOffAnimator.isRunning()) {
            return;
        }
        // 创建一个值动画，值从mRadius变化到0
        mOffAnimator = ValueAnimator.ofInt(mRadius, 0);
        // 设置动画的持续时间为200毫秒
        mOffAnimator.setDuration(200);
        // 设置动画的插值器，这里使用了一个减速插值器
        mOffAnimator.setInterpolator(AnimationUtils.loadInterpolator(getContext(), R.anim.decelerate_interpolator_more));
        // 设置动画的更新监听器
        mOffAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                // 获取当前动画的值
                int cValue = (int) valueAnimator.getAnimatedValue();
                // 计算当前的透明度（避免除0）
                float alpha = mRadius > 0 ? (cValue * 1f / mRadius) : 1f;
                // 获取子控件的数量
                int childCount = getChildCount();
                // 遍历所有的子控件，设置它们的透明度
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setAlpha(alpha);
                }
                // 更新面板半径
                mPanelRadius = cValue;
                // 请求重新布局
                requestLayout();
            }
        });
        // 设置动画的监听器
        mOffAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 当动画结束时，将所有的子控件设置为不可见
                int childCount = getChildCount();
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setVisibility(View.GONE);
                }
                // 如果设置了面板切换监听器，那么通知监听器面板已经关闭
                if (mTogglePanelListener != null) {
                    mTogglePanelListener.onToggleChange(false);
                }
                // 将mOffAnimator设置为null
                mOffAnimator = null;
            }
        });
        // 开始动画
        mOffAnimator.start();
    }

    public void setOrientation(boolean isLeft) {
        // 如果当前的方向和设置的方向不一样
        if (this.isLeft != isLeft) {
            // 更新方向
            this.isLeft = isLeft;
            // 设置开始角度
            setStartAngle();
            // 请求重新布局
            requestLayout();
        }
    }

    /**
     * 修正跟随位置
     */
    public int[] followButtonPosition(int x, int y) {
        // 创建一个数组用于存储结果
        int[] result = new int[2];
        // 如果是在左边，那么x坐标就是传入的x
        if (isLeft) {
            result[0] = x;
        } else {
            // 如果是在右边，那么x坐标就是传入的x减去宽度加上子控件半径的两倍
            result[0] = x - mWidth + (mChildRadius * 2);
        }
        // y坐标是传入的y减去高度的一半加上子控件半径
        result[1] = y - (mHeight / 2) + mChildRadius;
        // 返回结果
        return result;
    }

    public interface OnTogglePanelListener {
        /**
         * 当切换开关状态时回调
         *
         * @param isOpen 当前是否是打开
         */
        void onToggleChange(boolean isOpen);
    }

    public interface OnPanelSizeChangeCallback {
        // 当面板大小改变时回调
        void onPanelSizeChange(int newWidth, int newHeight);
    }

    public void setOnPanelSizeChangeCallback(OnPanelSizeChangeCallback onPanelSizeChangeCallback) {
        // 设置面板大小改变的回调
        this.mOnPanelSizeChangeCallback = onPanelSizeChangeCallback;
    }

    public void setOnTogglePanelListener(OnTogglePanelListener togglePanelListener) {
        // 设置切换面板监听器
        this.mTogglePanelListener = togglePanelListener;
    }



    public static DisplayMetrics getDisplayMetrics(Context context) {
        // 获取屏幕的显示指标
        return context.getResources().getDisplayMetrics();
    }
}